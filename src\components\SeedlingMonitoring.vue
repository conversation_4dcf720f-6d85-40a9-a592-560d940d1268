<template>
    <div class="seedling-monitoring">
        <!-- 有视频数据时显示视频 -->
        <div class="seedling-video-container" v-if="hasVideoData">
            <video
                ref="mainVideoElement"
                class="seedling-video-player"
                :src="videoSrc"
                preload="metadata"
                controls
                @error="handleVideoError"
            >
                您的浏览器不支持视频播放
            </video>
            <!-- 打开弹窗按钮 -->
            <div class="main-video-controls">
                <div class="control-right">
                    <button class="main-control-btn dialog-btn" @click.stop="openSeedlingDialog">
                        <img :src="allScreenIcon" alt="打开弹窗" />
                    </button>
                </div>
            </div>
        </div>

        <!-- 无视频数据时显示无数据样式 -->
        <div class="seedling-no-data-container" v-else @click="openSeedlingDialog">
            <div class="seedling-no-data-content">
                <img src="../assets/image/monitoringCenter/noData.png" alt="无数据" />
                <div class="seedling-no-data-text">暂无视频数据</div>
            </div>
        </div>

        <!-- 苗情监测弹窗 -->
        <div class="seedlingMonitorDialog dialogStyle">
            <el-dialog
                title="苗情监测"
                width="1570px"
                :visible.sync="dialogVisible"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                center
            >
                <img src="../assets/image/eppoWisdom/close.png" alt="" class="clone" @click="closeSeedlingDialog" />

                <div class="wire"></div>
                <div class="content">
                    <!-- 第一行：查询表单 -->
                    <div class="form-row">
                        <div class="form-left">
                            <div class="form-item formStyle">
                                <el-select
                                    v-model="selectedArea"
                                    placeholder="请选择种植区域"
                                    popper-class="selectStyle_list"
                                    :loading="loading"
                                    @change="onAreaChange"
                                >
                                    <el-option
                                        v-for="area in areaOptions"
                                        :key="area.value"
                                        :label="area.label"
                                        :value="area.value"
                                    ></el-option>
                                </el-select>
                            </div>
                            <div class="form-item formStyle">
                                <el-select
                                    v-model="selectedCamera"
                                    placeholder="请选择设备"
                                    popper-class="selectStyle_list"
                                    :loading="loading"
                                    :disabled="!selectedArea"
                                    @change="onCameraChange"
                                >
                                    <el-option
                                        v-for="camera in cameraOptions"
                                        :key="camera.value"
                                        :label="camera.label"
                                        :value="camera.value"
                                    ></el-option>
                                </el-select>
                            </div>
                            <div class="form-item searchButtonStyle2">
                                <el-button
                                    @click="capturePhoto"
                                    :loading="loading"
                                    :disabled="!selectedCamera"
                                >立即拍摄</el-button>
                            </div>
                        </div>
                        <div class="form-right">
                            <div class="searchButtonStyle2">
                                <el-button @click="openVideoPlayback">视频回放</el-button>
                            </div>
                            <div class="searchButtonStyle2">
                                <el-button @click="openHistoryImages">历史图片</el-button>
                            </div>
                        </div>
                    </div>

                    <!-- 主要内容区域 -->
                    <div class="main-content">
                        <!-- 左侧视频监控区域 -->
                        <div class="video-section">
                            <div class="video-container">
                                <div class="video-player" v-if="dialogVideoSrc">
                                    <video
                                        ref="videoElement"
                                        class="video-element"
                                        :src="dialogVideoSrc"
                                        preload="metadata"
                                        controls
                                        @error="handleDialogVideoError"
                                    >
                                        您的浏览器不支持视频播放
                                    </video>
                                </div>
                                <div class="no-video" v-else>
                                    <img src="../assets/image/monitoringCenter/noData.png" alt="无数据" />
                                    <div class="no-video-text">暂无视频数据</div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧图片展示区域 -->
                        <div class="image-section">
                            <!-- 大图片 -->
                            <div class="large-image">
                                <img :src="selectedImage" alt="选中图片" v-if="selectedImage" />
                                <div class="no-image" v-else>
                                    <img src="../assets/image/monitoringCenter/noData.png" alt="无数据" />
                                    <div class="no-image-text">暂无图片数据</div>
                                </div>
                            </div>

                            <!-- 小图片列表 -->
                            <div class="small-images">
                                <div
                                    class="small-image"
                                    v-for="(image, index) in imageList"
                                    :key="index"
                                    @click="selectImage(image)"
                                    :class="{ active: selectedImage === image }"
                                >
                                    <img :src="image" alt="缩略图" />
                                </div>
                                <!-- 占位图片，确保有3个位置 -->
                                <div
                                    class="small-image placeholder"
                                    v-for="n in (3 - imageList.length)"
                                    :key="'placeholder-' + n"
                                    v-if="imageList.length < 3"
                                >
                                    <img src="../assets/image/monitoringCenter/noData.png" alt="无数据" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>

        <!-- 视频回放弹窗 -->
        <NewSeedlingMonitoring
            :visible.sync="videoPlaybackVisible"
            @go-to-realtime="handleGoToRealtime"
        />

        <!-- 历史图片弹窗 -->
        <SeedlingGrowthHistoryPhotoDialog />
    </div>
</template>

<script>
import NewSeedlingMonitoring from './NewSeedlingMonitoring.vue'
import SeedlingGrowthHistoryPhotoDialog from './Dialog/seedlingGrowthHistoryPhotoDialog.vue'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService.js'
import CropCaseService from '../jaxrs/concrete/com.zny.ia.api.CropCaseService.js'

export default {
    name: 'SeedlingMonitoring',
    components: {
        NewSeedlingMonitoring,
        SeedlingGrowthHistoryPhotoDialog,
    },
    data() {
        return {
            hasVideoData: false, // 控制是否有视频数据，默认为false
            videoSrc: '', // 视频源地址

            // 弹窗相关数据
            dialogVisible: false, // 弹窗显示状态
            selectedArea: '', // 选中的种植区域
            selectedCamera: '', // 选中的摄像头
            dialogVideoSrc: '', // 弹窗中的视频源
            videoPlaybackVisible: false, // 视频回放弹窗显示状态

            selectedImage: '', // 选中的大图片
            imageList: [], // 图片列表


            // 种植区域选项 - 从接口获取
            areaOptions: [],

            // 摄像头选项 - 从接口获取
            cameraOptions: [],

            // 加载状态
            loading: false
        }
    },
    mounted() {
        // 获取区域列表
        this.getAreaList();

        // 监听上传图片成功事件
        this.listen('seedlingGrowthUploadSuccess', () => {
            // 刷新当前设备的监控数据和图片
            if (this.selectedCamera) {
                this.getMonitoringVideo(this.selectedCamera);
            }
        });
    },
    beforeDestroy() {
        // 清除事件监听器
        this.removeAllListener();
    },
    methods: {
        // 获取区域列表
        getAreaList() {
            this.loading = true;
            CommonService.allAreaOfCurrentUserManage()
                .then(res => {
                    this.areaOptions = res.map(area => ({
                        value: area.areaId,
                        label: area.areaName
                    }));
                })
                .catch(err => {
                    console.error('获取区域列表失败:', err);
                    this.$message.error('获取区域列表失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        // 根据区域ID获取设备列表
        getEquipmentList(areaId) {
            if (!areaId) {
                this.cameraOptions = [];
                return;
            }

            this.loading = true;
            CommonService.equipmentListByAreaId(areaId)
                .then(res => {
                    this.cameraOptions = res.map(equipment => ({
                        value: equipment.equipmentId,
                        label: equipment.equipmentName
                    }));
                })
                .catch(err => {
                    console.error('获取设备列表失败:', err);
                    this.$message.error('获取设备列表失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        // 获取监控录像
        getMonitoringVideo(equipmentIds) {
            if (!equipmentIds) {
                this.hasVideoData = false;
                this.dialogVideoSrc = '';
                return;
            }

            this.loading = true;
            CropCaseService.cropCaseMonitoring(equipmentIds)
                .then(res => {
                    if (res && res.videoLiveUrl) {
                        this.dialogVideoSrc = res.videoLiveUrl;
                        this.hasVideoData = true;
                        console.log('获取到监控视频:', res.videoLiveUrl);
                    } else {
                        this.hasVideoData = false;
                        this.dialogVideoSrc = '';
                    }

                    // 更新图片列表 - 直接使用接口返回的图片数据
                    if (res && res.pictureDataVoList && res.pictureDataVoList.length > 0) {
                        // 获取最新的4张图片
                        const latestPictures = res.pictureDataVoList.slice(0, 4).map(item => item.pictureUrl);
                        this.imageList = latestPictures;
                        // 设置第一张图片为选中状态
                        this.selectedImage = latestPictures[0] || '';
                        console.log('获取到设备图片:', latestPictures);
                    } else {
                        this.imageList = [];
                        this.selectedImage = '';
                    }
                })
                .catch(err => {
                    console.error('获取监控录像失败:', err);
                    this.$message.error('获取监控录像失败');
                    this.hasVideoData = false;
                    this.dialogVideoSrc = '';
                })
                .finally(() => {
                    this.loading = false;
                });
        },





        // 处理视频加载错误
        handleVideoError() {
            console.error('苗情视频加载失败');
            this.hasVideoData = false;
        },

        // 打开苗情监测弹窗
        openSeedlingDialog() {
            this.dialogVisible = true;
            // 设置默认选项
            if (this.areaOptions.length > 0) {
                this.selectedArea = this.areaOptions[0].value;
                // 获取该区域的设备列表
                this.getEquipmentList(this.selectedArea);
            }
        },

        // 关闭苗情监测弹窗
        closeSeedlingDialog() {
            this.dialogVisible = false;
            this.dialogVideoSrc = '';
            this.selectedArea = '';
            this.selectedCamera = '';
            this.cameraOptions = [];
            this.imageList = [];
            this.selectedImage = '';
        },

        // 区域选择改变
        onAreaChange(areaId) {
            this.selectedCamera = '';
            this.dialogVideoSrc = '';
            this.imageList = [];
            this.selectedImage = '';
            // 获取该区域的设备列表
            this.getEquipmentList(areaId);
        },

        // 摄像头选择改变
        onCameraChange(equipmentId) {
            if (equipmentId) {
                // 获取监控录像
                this.getMonitoringVideo(equipmentId);
            } else {
                this.dialogVideoSrc = '';
                // 注意：图片列表由区域选择控制，这里不清空
            }
        },

        // 处理弹窗视频加载错误
        handleDialogVideoError() {
            console.error('苗情弹窗视频加载失败')
            this.dialogVideoSrc = ''
        },

        // 立即拍摄
        capturePhoto() {
            if (!this.selectedCamera) {
                this.$message.warning('请先选择设备');
                return;
            }

            this.loading = true;
            CropCaseService.realTimeShoot(this.selectedCamera)
                .then(() => {
                    this.$message.success('拍摄成功！');
                    // 重新获取监控数据以更新图片列表
                    this.getMonitoringVideo(this.selectedCamera);
                })
                .catch(err => {
                    console.error('拍摄失败:', err);
                    this.$message.error('拍摄失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        // 打开视频回放
        openVideoPlayback() {
            this.dialogVisible = false // 关闭当前弹窗
            this.videoPlaybackVisible = true // 打开视频回放弹窗
        },

        // 打开历史图片
        openHistoryImages() {
            if (!this.selectedArea) {
                this.$message.warning('请先选择区域');
                return;
            }
            // 触发历史图片弹窗打开事件
            this.zEmit('seedlingGrowthHistoryPhotoDialogOpen', this.selectedArea);
        },

        // 选择图片
        selectImage(image) {
            this.selectedImage = image
        },



        // 从视频回放返回实时监控
        handleGoToRealtime() {
            this.videoPlaybackVisible = false // 关闭视频回放弹窗
            this.dialogVisible = true // 打开苗情监测弹窗
        }
    }
}
</script>

<style lang="less" scoped>
.seedling-monitoring {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    .seedling-video-container {
        width: 386px;
        height: 217px;
        position: relative;
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .seedling-video-player {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #000;
        }

        // 主视频控制按钮
        .main-video-controls {
            position: absolute;
            bottom: 8px;
            right: 8px;
            display: flex;
            align-items: center;

            .control-right {
                display: flex;
                align-items: center;
            }

            .main-control-btn {
                width: 32px;
                height: 32px;
                background: transparent;
                border: none;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    transform: scale(1.05);
                }

                img {
                    width: 20px;
                    height: 20px;
                    filter: brightness(0) invert(1); // 将图标变为白色
                }
            }

            .dialog-btn {
                img {
                    width: 18px;
                    height: 18px;
                }
            }
        }
    }
    
    .seedling-no-data-container {
        width: 386px;
        height: 217px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);
        }

        .seedling-no-data-content {
            text-align: center;

            img {
                width: 60px;
                height: 60px;
                margin-bottom: 12px;
                opacity: 0.6;
            }

            .seedling-no-data-text {
                color: rgba(255, 255, 255, 0.6);
                font-size: 16px;
                font-weight: 400;
            }
        }
    }
}

// 苗情监测弹窗样式
.seedlingMonitorDialog ::v-deep .el-dialog {
    height: 670px;
    margin-top: calc(50vh - 219px) !important;

    .el-dialog__header {
        padding: 20px 20px 0 20px;
        text-align: left;

        .el-dialog__title {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #DFEEF3;
            position: relative;
        }
    }

    .el-dialog__body {
        height: calc(100% - 74px);
        padding: 13px 30px 35px 36px;

        .clone {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            z-index: 10;
        }

        .wire {
            height: 2px;
            background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
            margin-bottom: 20px;
        }

        .content {
            height: calc(100% - 22px);

            // 第一行表单样式
            .form-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                height: 32px;

                .form-left {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    .form-item {
                        width: 180px;
                        height: 32px;

                        &.searchButtonStyle2 {
                            width: 110px;
                        }
                    }
                }

                .form-right {
                    display: flex;
                    gap: 20px;

                    .searchButtonStyle2 {
                        width: 110px;
                        height: 32px;
                    }
                }
            }

            // 主要内容区域
            .main-content {
                display: flex;
                gap: 36px;
                height: calc(100% - 52px);

                // 左侧视频区域
                .video-section {
                    width: 837px;
                    height: 471px;

                    .video-container {
                        width: 849px;
                        height: 483px;
                        border: 2px solid rgba(0, 228, 255, 0.8);
                        // border-radius: 2px;
                        overflow: hidden;
                        background: #000;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .video-player {
                            width: 837px;
                            height: 471px;
                            position: relative;

                            .video-element {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                                background: #000;
                            }


                        }

                        .no-video {
                            width: 837px;
                            height: 471px;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            background-color: #fff;
                            img {
                                width: 80px;
                                height: 80px;
                                margin-bottom: 20px;
                                opacity: 0.6;
                            }

                            .no-video-text {
                                color: rgba(255, 255, 255, 0.6);
                                font-size: 18px;
                                font-weight: 400;
                            }
                        }
                    }
                }

                // 右侧图片区域
                .image-section {
                    width: 631px;
                    height: 471px;

                    .large-image {
                        width: 631px;
                        height: 355px;
                        border: 1px solid rgba(0, 255, 255, 0.3);
                        border-radius: 4px;
                        overflow: hidden;
                        background: #000;
                        margin-bottom: 16px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }

                        .no-image {
                            width: 100%;
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;

                            img {
                                width: 60px;
                                height: 60px;
                                margin-bottom: 15px;
                                opacity: 0.6;
                            }

                            .no-image-text {
                                color: rgba(255, 255, 255, 0.6);
                                font-size: 16px;
                                font-weight: 400;
                            }
                        }
                    }

                    .small-images {
                        display: flex;
                        gap: 20px;
                        height: 111px;

                        .small-image {
                            width: 197px;
                            height: 111px;
                            border: 1px solid rgba(0, 255, 255, 0.3);
                            border-radius: 4px;
                            overflow: hidden;
                            background: #000;
                            cursor: pointer;
                            transition: all 0.3s ease;

                            &:hover {
                                border-color: rgba(0, 255, 255, 0.6);
                                box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
                            }

                            &.active {
                                border-color: #00f4fd;
                                box-shadow: 0 0 15px rgba(0, 244, 253, 0.5);
                            }

                            &.placeholder {
                                cursor: default;
                                opacity: 0.5;

                                &:hover {
                                    border-color: rgba(0, 255, 255, 0.3);
                                    box-shadow: none;
                                }
                            }

                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>

<style lang="less">
@import '../assets/css/meteorology.less';
@import '../assets/css/waterMonitor.less';
</style>
