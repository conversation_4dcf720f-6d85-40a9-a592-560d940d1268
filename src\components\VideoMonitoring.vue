<template>
    <div class="video-monitoring">
        <!-- 有视频数据时显示视频 -->
        <div class="video-container" v-if="hasVideoData" @click="openVideoDialog">
            <video
                ref="mainVideoElement"
                class="video-player"
                :src="videoSrc"
                preload="metadata"
                @error="handleVideoError"
            >
                您的浏览器不支持视频播放
            </video>

        </div>

        <!-- 无视频数据时显示无数据样式 -->
        <div class="no-data-container" v-else @click="openVideoDialog">
            <div class="no-data-content">
                <img src="../assets/image/monitoringCenter/noData.png" alt="无数据" />
                <div class="no-data-text">暂无视频数据</div>
            </div>
        </div>

        <!-- 视频监控弹窗 -->
        <div class="meteorologyEMDialog dialogStyle videoMonitorDialog">
            <el-dialog
                title="视频监控"
                width="1256px"
                :visible.sync="dialogVisible"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                center
            >
                <img src="../assets/image/eppoWisdom/close.png" alt="" class="clone" @click="closeVideoDialog" />

                <div class="wire"></div>
                <div class="content">
                    <!-- 控制区域 -->
                    <div class="control-area">
                        <div class="control-item formStyle">
                            <el-select
                                v-model="selectedArea"
                                placeholder="请选择区域"
                                popper-class="selectStyle_list"
                                :loading="loading"
                                @change="onAreaChange"
                            >
                                <el-option
                                    v-for="area in areaOptions"
                                    :key="area.value"
                                    :label="area.label"
                                    :value="area.value"
                                ></el-option>
                            </el-select>
                        </div>
                        <div class="control-item formStyle">
                            <el-select
                                v-model="selectedDevice"
                                placeholder="请选择设备"
                                popper-class="selectStyle_list"
                                :loading="loading"
                                :disabled="!selectedArea"
                                @change="onDeviceChange"
                            >
                                <el-option
                                    v-for="device in deviceOptions"
                                    :key="device.value"
                                    :label="device.label"
                                    :value="device.value"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>

                    <!-- 视频播放区域 -->
                    <div class="video-dialog-container">
                        <div class="video-border">
                            <div class="video-window">
                                <!-- 视频内容区域 -->
                                <div class="video-content" v-if="dialogVideoSrc">
                                    <video
                                        ref="videoElement"
                                        class="video-element"
                                        :src="dialogVideoSrc"
                                        controls
                                        preload="metadata"
                                        @error="handleDialogVideoError"
                                    >
                                        您的浏览器不支持视频播放
                                    </video>

                                </div>
                                <div class="video-placeholder" v-else>
                                    <img src="../assets/image/monitoringCenter/videoPlay.png" alt="视频播放" class="video-placeholder-icon" />
                                    <div class="video-info">暂无视频数据</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService.js'
import CropCaseService from '../jaxrs/concrete/com.zny.ia.api.CropCaseService.js'
import HomePageService from '../jaxrs/concrete/com.zny.ia.api.HomePageService.js'

export default {
    name: 'VideoMonitoring',
    data() {
        return {
            hasVideoData: false, // 控制是否有视频数据，暂时设为false
            videoSrc: '', // 视频源地址，待接口接入时使用

            // 弹窗相关数据
            dialogVisible: false, // 弹窗显示状态
            selectedArea: '', // 选中的区域
            selectedDevice: '', // 选中的设备
            dialogVideoSrc: '', // 弹窗中的视频源



            // 区域选项 - 从接口获取
            areaOptions: [],

            // 设备选项 - 从接口获取
            deviceOptions: [],

            // 加载状态
            loading: false
        }
    },
    mounted() {
        // 获取区域列表
        this.getAreaList()

        // 初始化视频数据
        this.loadVideoData()

        // 监听地图区域选择变化事件（监控中心）
        this.$root.$on('video-monitoring-area-change', this.handleAreaSelectionChange)
    },
    beforeDestroy() {
        // 移除事件监听
        this.$root.$off('video-monitoring-area-change', this.handleAreaSelectionChange)
    },
    methods: {
        // 获取区域列表（使用与地图相同的数据源）
        getAreaList() {
            this.loading = true
            HomePageService.findAreaCrop()
                .then(res => {
                    this.areaOptions = res.map(area => ({
                        value: area.areaId,
                        label: area.areaName
                    }))
                })
                .catch(err => {
                    console.error('获取区域列表失败:', err)
                    this.$message.error('获取区域列表失败')
                })
                .finally(() => {
                    this.loading = false
                })
        },

        // 根据区域ID获取设备列表
        getEquipmentList(areaId) {
            if (!areaId) {
                this.deviceOptions = []
                return Promise.resolve()
            }

            this.loading = true
            return CommonService.equipmentListByAreaId(areaId)
                .then(res => {
                    this.deviceOptions = res.map(equipment => ({
                        value: equipment.equipmentId,
                        label: equipment.equipmentName
                    }))
                })
                .catch(err => {
                    console.error('获取设备列表失败:', err)
                    this.$message.error('获取设备列表失败')
                })
                .finally(() => {
                    this.loading = false
                })
        },

        // 加载视频数据
        loadVideoData() {
            // 如果有默认的区域和设备，获取实时监控
            if (this.areaOptions.length > 0) {
                const defaultArea = this.areaOptions[0].value
                this.getEquipmentList(defaultArea).then(() => {
                    if (this.deviceOptions.length > 0) {
                        const defaultDevice = this.deviceOptions[0].value
                        this.getRealTimeVideo(defaultDevice)
                    }
                })
            } else {
                // 暂时设置为无数据状态
                this.hasVideoData = false
            }
        },

        // 获取实时监控视频
        getRealTimeVideo(equipmentId) {
            if (!equipmentId) {
                this.hasVideoData = false
                return
            }

            this.loading = true
            CropCaseService.cropCaseMonitoring(equipmentId)
                .then(res => {
                    if (res && res.videoRealTimeUrl) {
                        this.videoSrc = res.videoRealTimeUrl
                        this.hasVideoData = true
                        console.log('获取到实时视频:', this.videoSrc)
                    } else {
                        this.hasVideoData = false
                        console.log('未获取到实时视频数据')
                    }
                })
                .catch(err => {
                    console.error('获取实时视频失败:', err)
                    this.hasVideoData = false
                })
                .finally(() => {
                    this.loading = false
                })
        },


        
        // 处理视频加载错误
        handleVideoError() {
            console.error('视频加载失败');
            this.hasVideoData = false;
        },

        // 打开视频弹窗
        openVideoDialog() {
            this.dialogVisible = true
            // 设置默认选项
            if (this.areaOptions.length > 0) {
                this.selectedArea = this.areaOptions[0].value
                this.getEquipmentList(this.selectedArea).then(() => {
                    if (this.deviceOptions.length > 0) {
                        this.selectedDevice = this.deviceOptions[0].value
                        this.loadDialogVideo()
                    }
                })
            }
        },

        // 关闭视频弹窗
        closeVideoDialog() {
            this.dialogVisible = false
            this.dialogVideoSrc = ''
        },



        // 区域选择改变
        onAreaChange(areaId) {
            console.log('区域选择改变:', areaId)
            this.selectedDevice = ''
            this.dialogVideoSrc = ''
            // 获取该区域的设备列表
            this.getEquipmentList(areaId)
        },

        // 设备选择改变
        onDeviceChange(equipmentId) {
            console.log('设备选择改变:', equipmentId)
            this.loadDialogVideo()
        },

        // 加载弹窗视频
        loadDialogVideo() {
            if (!this.selectedArea || !this.selectedDevice) {
                this.dialogVideoSrc = ''
                return
            }

            this.loading = true
            CropCaseService.cropCaseMonitoring(this.selectedDevice)
                .then(res => {
                    if (res && res.videoRealTimeUrl) {
                        this.dialogVideoSrc = res.videoRealTimeUrl
                        console.log('获取到弹窗实时视频:', this.dialogVideoSrc)
                    } else {
                        this.dialogVideoSrc = ''
                        console.log('未获取到弹窗实时视频数据')
                    }
                })
                .catch(err => {
                    console.error('获取弹窗实时视频失败:', err)
                    this.dialogVideoSrc = ''
                })
                .finally(() => {
                    this.loading = false
                })
        },

        // 处理弹窗视频加载错误
        handleDialogVideoError() {
            console.error('弹窗视频加载失败')
            this.dialogVideoSrc = ''
        },

        // 处理地图区域选择变化事件
        handleAreaSelectionChange(areaId) {
            console.log('视频监控接收到地图区域选择变化:', areaId)

            // 更新主视频显示的区域
            if (areaId) {
                // 获取该区域的设备列表
                this.getEquipmentList(areaId).then(() => {
                    if (this.deviceOptions.length > 0) {
                        // 自动选择第一个设备并加载实时视频
                        const firstDevice = this.deviceOptions[0].value
                        this.getRealTimeVideo(firstDevice)
                    } else {
                        // 没有设备时清空视频
                        this.hasVideoData = false
                        this.videoSrc = ''
                    }
                })
            }
        }
    }
}
</script>

<style lang="less" scoped>
.video-monitoring {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    .video-container {
        width: 386px;
        height: 217px;
        position: relative;
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .video-player {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #000;
        }




    }
    
    .no-data-container {
        width: 386px;
        height: 217px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);
        }

        .no-data-content {
            text-align: center;

            img {
                width: 60px;
                height: 60px;
                margin-bottom: 12px;
                opacity: 0.6;
            }

            .no-data-text {
                color: rgba(255, 255, 255, 0.6);
                font-size: 16px;
                font-weight: 400;
            }
        }
    }
}

// 弹窗样式
.videoMonitorDialog ::v-deep .el-dialog {
    height: 854px;
    margin-top: 113px !important; // 调整弹窗垂直位置，可根据需要修改
    // margin-left: auto !important; // 水平居中，如需偏移可设置具体数值

    .el-dialog__header {
        padding: 20px 20px 0 20px;
        text-align: left; // title居中显示

        .el-dialog__title {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #DFEEF3;
            position: relative;
            // left: 0; // 如需微调title位置，可修改此值
        }
    }

    .el-dialog__body {
        height: calc(100% - 74px); // 调整body高度以适应header
        padding: 13px 30px 30px 30px;

        .clone {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            z-index: 10;
        }

        .wire {
            height: 2px;
            background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
            margin-bottom: 20px;
        }

        .content {
            height: calc(100% - 22px);

            .control-area {
                display: flex;
                width: 1120px;
                gap: 30px;
                margin-bottom: 20px;
                margin-left: 20px;

                .control-item {
                    display: flex;
                    align-items: center;
                    height: 32px;
                    color:#FEFFFF;

                    .control-label {
                        font-size: 16px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #DFEEF3;
                        margin-right: 10px;
                        white-space: nowrap;
                    }

                    .el-select {
                        width: 200px;
                    }
                }
            }

            .video-dialog-container {
                width: 100%;
                display: flex;
                justify-content: center;

                .video-border {
                    width: 1152px;
                    height: 662px;
                    border: 1px solid rgba(0, 244, 253, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: rgba(0, 0, 0, 0.1);

                    .video-window {
                        width: 1120px;
                        height: 630px;
                        background: #000000;
                        border-radius: 2px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        position: relative;

                        .video-content {
                            width: 100%;
                            height: 100%;
                            position: relative;

                            .video-element {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }


                        }

                        .video-placeholder {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;

                            .video-placeholder-icon {
                                width: 64px;
                                height: 64px;
                                opacity: 0.5;
                                margin-bottom: 20px;
                            }

                            .video-info {
                                font-size: 16px;
                                font-family: Microsoft YaHei;
                                color: #DFEEF3;
                                opacity: 0.7;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 自定义下拉选择框样式
.videoMonitorDialog ::v-deep .formStyle {
    .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
    }

    .el-select .el-input .el-select__caret {
        color: rgba(254, 255, 255, 0.6) !important;
    }
}
</style>

<style lang="less">
@import '../assets/css/meteorology.less';
@import '../assets/css/waterMonitor.less';
</style>
